# Admin Upgrade Tasks

## System Menu
- [x] Add a new "System" menu item to the admin toolbar
- [x] Create submenu items for "Site Health", "Database Migrations", and "Error Logs"

## Site Health Page
- [x] Create a new page to show site statistics
- [x] Display server software details (DB version, Node.js version, system information, etc.)

## Database Migrations Page
- [x] Create a new page to run migrations from the prisma folder
- [x] Create a table in the database to track migration details
- [x] Implement functionality to track migration status (ran/not ran, success/failure)
- [x] Show errors with migration status if failed
- [x] Allow migrations to be skipped
- [x] Add a button to scan for new migration files

## Error Logs Page
- [x] Create a new page to show site errors from Sentry
- [x] Implement integration with Sentry API
- [x] Allow users to interact with errors (resolve, etc.)
- [x] Display detailed information about each error
