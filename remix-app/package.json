{"name": "remix-app", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix build", "dev": "remix dev --manual", "start": "NODE_OPTIONS='--import ./instrumentation.server.mjs' remix-serve ./build/index.js", "typecheck": "tsc", "build:check": "tsc -b"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/helpers": "^0.0.9", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/react": "^0.0.9", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@googlemaps/google-maps-services-js": "^3.3.39", "@headlessui/react": "^1.7.18", "@json2csv/plainjs": "^7.0.6", "@marsidev/react-turnstile": "^0.4.0", "@prisma/extension-optimize": "^1.1.4", "@remix-run/css-bundle": "^2.2.0", "@remix-run/node": "^2.2.0", "@remix-run/react": "^2.2.0", "@remix-run/serve": "^2.2.0", "@sentry/remix": "^9.5.0", "@superset-ui/embedded-sdk": "^0.1.0-alpha.10", "@tinymce/tinymce-react": "^5.1.1", "aws-sdk": "^2.1542.0", "bcryptjs": "^2.4.3", "chart.js": "^4.4.8", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "downshift": "^8.3.1", "geocodio-library-node": "^1.6.1", "html-react-parser": "^5.1.10", "html2canvas": "^1.4.1", "is-ip": "^5.0.1", "isbot": "^3.6.8", "isomorphic-dompurify": "^2.12.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lru-cache": "^11.1.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "node-html-to-image": "^5.0.0", "openai": "^4.0.0", "phantomjs-prebuilt": "^2.1.16", "prisma-client": "^0.0.0", "prisma-redis-middleware": "^4.8.0", "qs": "^6.11.2", "quirrel": "^1.14.1", "rc-pagination": "^4.2.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-google-autocomplete": "^2.7.4", "react-json-view-lite": "^2.3.0", "react-leaflet": "^4.2.1", "react-markdown": "^10.0.0", "react-multi-ref": "^1.0.2", "react-scroll": "^1.9.0", "react-textarea-autosize": "^8.5.7", "rehype-raw": "^7.0.0", "remix-auth": "^3.7.0", "remix-utils": "^7.6.0", "sharp": "^0.33.5", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sparkpost": "^2.1.4", "superjson": "^2.2.1", "tiny-invariant": "^1.3.1"}, "devDependencies": {"@prisma/client": "^6.2.1", "@remix-run/dev": "^2.2.0", "@remix-run/eslint-config": "^2.2.0", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.0", "@types/express": "^5.0.2", "@types/google.maps": "^3.58.1", "@types/html2canvas": "^0.5.35", "@types/jsonwebtoken": "^9.0.9", "@types/jspdf": "^1.3.3", "@types/leaflet": "^1.9.12", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/qs": "^6.9.11", "@types/rc-select": "^8.6.5", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/react-scroll": "^1.8.10", "@types/socket.io": "^3.0.2", "@types/sparkpost": "^2.1.8", "@types/testing-library__react": "^10.0.1", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "eslint": "latest", "eslint-config-prettier": "latest", "eslint-plugin-jsx-a11y": "latest", "eslint-plugin-prettier": "latest", "eslint-plugin-react": "latest", "eslint-plugin-react-hooks": "latest", "eslint-plugin-tailwindcss": "latest", "prettier": "latest", "prisma": "^6.2.1", "stylelint": "latest", "stylelint-config-standard": "latest", "stylelint-config-tailwindcss": "latest", "tailwindcss": "^3.3.5", "typescript": "^5.4.5"}, "engines": {"node": ">=18.0.0"}}