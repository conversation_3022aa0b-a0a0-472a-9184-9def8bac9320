/*
@import url('https://fonts.googleapis.com/css?family=Onest');
*/
@import url('https://fonts.googleapis.com/css2?family=Onest:wght@100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: "Onest", system-ui, sans-serif;
    @apply text-black;
    @apply bg-gray-800;
  }

  header {
    @apply bg-white;
  }

  main {
    @apply bg-white;
  }

  h2 {
    @apply font-bold;
    font-size: 30px;
    line-height: 40px;
  }

  h3 {
    @apply font-bold;
    font-size: 25px;
    line-height: 35px;
  }

  .container {
    @apply px-5 lg:px-10;
  }
}

.page-title {
  padding: 22px 0;
}

input {
  @apply h-[50px] rounded-[5px] outline-0 px-2 sm:px-3 lg:px-5 text-lg;
}

input::placeholder {
  color: #888888;
}

input[type="date"] {
  position: relative;
  padding-right: 20px;
}

input[type="date"]::after {
  content: '';
  background: url('images/Icon-Calendar.svg') no-repeat;
  width: 16px;
  height: 16px;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
}

select {
  @apply h-[50px] rounded-[5px] outline-0 pl-2 sm:pl-3 lg:pl-5 text-lg;
  @apply bg-[right_8px_center] sm:bg-[right_12px_center] lg:bg-[right_20px_center] pr-[20px] sm:pr-[24px] lg:pr-[32px];
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url('images/Arrow.svg');
  background-repeat: no-repeat;
}

table.table-blue tbody tr:nth-child(odd) {
  @apply bg-blue-200;
}

table.table-gray thead tr {
  @apply bg-gray-800 text-white;
}

table.table-gray thead tr th {
  @apply px-5 py-2.5 align-top;
}

table.table-gray tbody tr td {
  @apply px-5 py-4 border-b;
  border-color: #DDDDDD;
}

table.table-gray tbody tr:nth-child(even) {
  background-color: #EEEEEE;
}

.incident table.table-participant thead tr {
  @apply bg-blue-500 text-white;
}

.incident table.table-participant thead tr th {
  @apply px-5 py-2.5 text-left;
}

.incident table.table-participant tbody tr td {
  @apply px-5 py-2.5;
}

.incident table.table-participant tbody tr:nth-child(even) {
  @apply bg-gray-200;
}

.paragraph p {
  margin-bottom: 26px;
}

.paragraph p:last-child {
  margin-bottom: 0;
}

.tabbar {
  @apply bg-gray-600 text-white
}

.tabbar-nav {
  @apply flex text-lg divide-x divide-gray-800 border-x border-gray-800;
}

.tabbar-nav .link {
  @apply block px-5 py-4;
  cursor: pointer;
}

.tabbar-nav .link.active {
  @apply bg-white text-black;
}

.bouncing-dots {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100px;
  height: 20px;
}

.bouncing-dots .dot {
  width: 10px;
  height: 10px;
  background-color: #333;
  border-radius: 50%;
  animation: bounce 0.6s infinite alternate;
}

.bouncing-dots .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.bouncing-dots .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  from {
    transform: translateY(5px);
  }

  to {
    transform: translateY(-5px);
  }
}

.querybuilder {}

.querybuilder .filter {
  @apply bg-gray-200 rounded-[10px] px-[20px] py-[14px] md:px-[30px] md:py-[24px];
}

.querybuilder .filter .title {
  @apply font-bold text-xl md:text-[25px] text-orange-500 mb-[14px] md:mb-[22px];
}

.querybuilder .filter .grid {
  @apply gap-[20px] xl:gap-[30px]
}

.querybuilder.basic {
  @apply pl-[30px];
}

.querybuilder.basic .operator {
  position: relative;
  top: -30px;
  left: -25px;
}

.querybuilder.basic .operator .andor {
  position: absolute;
  top: 30px;
  left: -24px;
  background-color: white;
  width: 50px;
  height: 30px;
  border-radius: 15px;
  border-width: 2px;
  font-weight: 700;
  font-size: 14px;
  text-align: center;
  line-height: 27px;
}

.querybuilder.advanced .filter {
  padding: 14px 20px;
}

.querybuilder.advanced .filter .title {
  @apply text-xl;
  margin-bottom: 14px;
}

.querybuilder.advanced .filter .grid {
  @apply gap-[20px]
}

.querybuilder.advanced .addfilter {
  @apply text-sm font-bold text-blue-500;
  margin-top: 19px;
}

.querybuilder.advanced .operator .andor {
  text-align: center;
  width: 50px;
  height: 30px;
  line-height: 30px;
  border-radius: 10px;
  border-width: 2px;
  font-size: 14px;
  font-weight: bold;
  /* line-height: 19px;
  padding-left: 8px;
  padding-right: 20px; */
  margin: 10px 0;
  /* background-position: right 8px center; */
}

.querybuilder.advanced .group {
  @apply p-[10px] md:p-[20px] lg:p-[30px];
  border-radius: 10px;
  border-width: 2px;
}

.querybuilder.advanced .group .anyall {
  @apply sm:text-lg;
  margin-bottom: 20px;
}

.querybuilder.advanced .group .anyall select {
  @apply bg-gray-200 mr-2.5;
  width: 100px;
}

ul.pagination {
  @apply flex space-x-[1px];
}

ul.pagination li {
  @apply bg-orange-500 text-white;
}

ul.pagination li a,
ul.pagination li button {
  @apply block px-4 py-1.5;
}

ul.pagination li.rc-pagination-prev {
  border-radius: 5px;
  margin-right: 20px;
}

ul.pagination li.rc-pagination-prev a {
  @apply px-3;
}

ul.pagination li.rc-pagination-next {
  border-radius: 5px;
  margin-left: 20px !important;
}

ul.pagination li.rc-pagination-next a {
  @apply px-3;
}

ul.pagination li.rc-pagination-item:nth-child(2) {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

ul.pagination li.rc-pagination-item:nth-last-child(2) {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

ul.pagination li.rc-pagination-jump-prev button:after {
  display: block;
  content: '...';
}

ul.pagination li.rc-pagination-jump-next button:after {
  display: block;
  content: '...';
}

ul.pagination li.rc-pagination-item-active {
  @apply bg-black text-white;
}

ul.pagination li.rc-pagination-disabled {
  @apply opacity-50;
}