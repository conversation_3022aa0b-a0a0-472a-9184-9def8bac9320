import type { LoaderFunctionArgs } from '@remix-run/node';
import { NavLink, Outlet } from '@remix-run/react';
import { AdminHeader } from '~/components/admin/Header';
import { useUser } from '~/utils/client-utils';

export const loader = async ({ request }: LoaderFunctionArgs) => {
	return {};
};

export default function AdminSystemPage() {
	const user = useUser();
	if (user.role != 'Admin') {
		throw new Error('No permission.');
	}

	return (
		<div className="min-h-screen bg-white">
			<AdminHeader />

			<main className="container mx-auto py-20">
				<div className="flex space-x-10">
					<div className="w-[350px] flex-none">
						<div className="rounded-[10px] bg-gray-200 p-10">
							<h2 className="mb-5 text-2xl font-bold">System</h2>
							<ul className="mt-5 divide-y divide-white border-y border-white text-lg">
								<li className="py-2.5">
									<NavLink
										className={({ isActive }) => `${isActive ? 'text-orange-500' : ''}`}
										to="/admin/system/health"
									>
										Site Health
									</NavLink>
								</li>
								<li className="py-2.5">
									<NavLink
										className={({ isActive }) => `${isActive ? 'text-orange-500' : ''}`}
										to="/admin/system/migrations"
									>
										Database Migrations
									</NavLink>
								</li>
								<li className="py-2.5">
									<NavLink
										className={({ isActive }) => `${isActive ? 'text-orange-500' : ''}`}
										to="/admin/system/errors"
									>
										Error Logs
									</NavLink>
								</li>
							</ul>
						</div>
					</div>

					<div className="flex-1">
						<Outlet />
					</div>
				</div>
			</main>
		</div>
	);
}
