import { json } from '@remix-run/node';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { useLoaderData } from '@remix-run/react';
import { useUser } from '~/utils/client-utils';
import { db } from '~/utils/db.server';

export const loader = async ({ request }: LoaderFunctionArgs) => {
	// Get database information
	const dbInfo = await db.$queryRaw<Array<{ version: string }>>`SELECT version() as version`;

	// Get Node.js information
	const nodeVersion = process.version;
	const nodeEnv = process.env.NODE_ENV || 'development';

	// Get system information
	const os = require('os');
	const systemInfo = {
		platform: os.platform(),
		release: os.release(),
		type: os.type(),
		arch: os.arch(),
		cpus: os.cpus().length,
		totalMemory: Math.round(os.totalmem() / (1024 * 1024 * 1024)) + ' GB',
		freeMemory: Math.round(os.freemem() / (1024 * 1024 * 1024)) + ' GB',
		uptime: Math.round(os.uptime() / 3600) + ' hours'
	};

	// Get application statistics
	const userCount = await db.users.count();
	const incidentCount = await db.incidents.count();

	return json({
		database: {
			version: dbInfo[0].version,
			provider: 'PostgreSQL',
			connectionString: '**hidden**' // Don't expose actual connection string
		},
		node: {
			version: nodeVersion,
			environment: nodeEnv
		},
		system: systemInfo,
		stats: {
			users: userCount,
			incidents: incidentCount
		},
		dependencies: {
			// Add key dependencies from package.json
			remix: process.env.npm_package_dependencies_remix || 'unknown',
			react: process.env.npm_package_dependencies_react || 'unknown',
			prisma: process.env.npm_package_dependencies_prisma || 'unknown'
		}
	});
};

export default function SiteHealthPage() {
	const user = useUser();
	if (user.role !== 'Admin') {
		throw new Error('No permission.');
	}

	const data = useLoaderData<typeof loader>();

	return (
		<div className="p-8">
			<h1 className="mb-8 text-3xl font-bold">Site Health</h1>

			<div className="grid grid-cols-1 gap-8 md:grid-cols-2">
				{/* Database Information */}
				<div className="rounded-lg bg-gray-100 p-6 shadow">
					<h2 className="mb-4 text-xl font-semibold">Database Information</h2>
					<div className="space-y-2">
						<div className="flex justify-between">
							<span className="font-medium">Version:</span>
							<span>{data.database.version}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Provider:</span>
							<span>{data.database.provider}</span>
						</div>
					</div>
				</div>

				{/* Node.js Information */}
				<div className="rounded-lg bg-gray-100 p-6 shadow">
					<h2 className="mb-4 text-xl font-semibold">Node.js Information</h2>
					<div className="space-y-2">
						<div className="flex justify-between">
							<span className="font-medium">Version:</span>
							<span>{data.node.version}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Environment:</span>
							<span>{data.node.environment}</span>
						</div>
					</div>
				</div>

				{/* System Information */}
				<div className="rounded-lg bg-gray-100 p-6 shadow">
					<h2 className="mb-4 text-xl font-semibold">System Information</h2>
					<div className="space-y-2">
						<div className="flex justify-between">
							<span className="font-medium">Platform:</span>
							<span>{data.system.platform}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">OS Type:</span>
							<span>{data.system.type}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">OS Release:</span>
							<span>{data.system.release}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Architecture:</span>
							<span>{data.system.arch}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">CPU Cores:</span>
							<span>{data.system.cpus}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Total Memory:</span>
							<span>{data.system.totalMemory}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Free Memory:</span>
							<span>{data.system.freeMemory}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Uptime:</span>
							<span>{data.system.uptime}</span>
						</div>
					</div>
				</div>

				{/* Application Statistics */}
				<div className="rounded-lg bg-gray-100 p-6 shadow">
					<h2 className="mb-4 text-xl font-semibold">Application Statistics</h2>
					<div className="space-y-2">
						<div className="flex justify-between">
							<span className="font-medium">Total Users:</span>
							<span>{data.stats.users}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Total Incidents:</span>
							<span>{data.stats.incidents}</span>
						</div>
					</div>
				</div>

				{/* Dependencies */}
				<div className="rounded-lg bg-gray-100 p-6 shadow md:col-span-2">
					<h2 className="mb-4 text-xl font-semibold">Key Dependencies</h2>
					<div className="grid grid-cols-2 gap-4 md:grid-cols-3">
						<div className="flex justify-between">
							<span className="font-medium">Remix:</span>
							<span>{data.dependencies.remix}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">React:</span>
							<span>{data.dependencies.react}</span>
						</div>
						<div className="flex justify-between">
							<span className="font-medium">Prisma:</span>
							<span>{data.dependencies.prisma}</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
