import { useEffect, useState } from 'react';
import { Link, useFetcher, useLoaderData } from '@remix-run/react';
import { SearchProvider } from '~/context/SearchContext';
import { FilterRegistry } from '~/services/search';
import type { FilterGroup, SearchState } from '~/types/search';
import { Header } from '~/components/common/Header';
import { Footer } from '~/components/common/Footer';
import Operator from '~/components/search/Operator';
import AddAnotherFilter from '~/components/search/AddAnotherFilter';
import FilterGroupComponent from '~/components/search/FilterGroupComponent2';
import Pagination from '~/components/Pagination';

export const loader = async () => {
	return {};
};

export default function AdvancedSearch() {
	// Get all available filters
	const availableFilters = FilterRegistry.getInstance().getAllAsMap();

	const fetcher = useFetcher();

	const [searchState, setSearchState] = useState<SearchState>({
		groups: [],
		page: 1,
		perPage: 20
	});

	const [displayAs, setDisplayAs] = useState('incident');

	const [results, setResults] = useState([]);
	const [totalCount, setTotalCount] = useState(0);

	const addGroup = (field: string, operator: string, value: any) => {
		setSearchState(prev => ({
			...prev,
			groups: [
				...prev.groups,
				{
					id: Date.now().toString(),
					type: 'group',
					operator: 'AND',
					filters: [
						{
							id: Date.now().toString(),
							type: 'filter',
							field,
							operator: operator as any, // Cast to correct type if needed
							value
						}
					]
				}
			]
		}));
	};

	const deleteGroup = (index: number) => {
		setSearchState(prev => ({
			...prev,
			groups: prev.groups.filter((_, i) => i !== index)
		}));
	};

	const updateGroup = (index: number, updatedGroup: FilterGroup) => {
		setSearchState(prev => ({
			...prev,
			groups: prev.groups.map((group, i) => i === index ? updatedGroup : group)
		}));
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		if (searchState.groups[0].filters.length === 0) {
			return;
		}
		setSearchState(prev => ({ ...prev, page: 1 }));
		onSearch({ ...searchState, page: 1 });
	};

	const onSearch = (search: SearchState) => {
		fetcher.submit(
			// @ts-ignore
			{
				baseTable: displayAs === 'incident' ? 'gva_data.incidents' : 'gva_data.incident_participants',
				baseAlias: 'inc',
				filterGroups: search.groups,
				page: search.page,
				pageSize: search.perPage,
				columns: displayAs === 'incident' ? ['incident_id', 'incident_date', 'state', 'city', 'address'] : ['participant_id', 'name'],
				format: 'array',
				userRoles: ['user']
			},
			{ method: 'post', action: '/api/search', encType: 'application/json' }
		);
	};

	const onPageChange = (page: number) => {
		setSearchState(prev => ({ ...prev, page }));
		onSearch({ ...searchState, page });
	};

	useEffect(() => {
		if (fetcher.data) {
			console.log('Fetcher data:', fetcher.data);
			setResults(fetcher.data.data);
			setTotalCount(fetcher.data.totalCount);
		}
	}, [fetcher.data]);

	useEffect(() => {
		console.log('searchState-------------', searchState);
	}, [searchState]);

	return (
		<SearchProvider filters={availableFilters}>
			<div className="min-h-screen">
				<Header />
				<main className="py-10 md:py-20">
					<div className="container mx-auto">
						{/* Search form */}
						<div>
							<div className="querybuilder advanced">
								{searchState.groups.map((group, index) => (
									<div key={`group-${group.id}`}>
										<FilterGroupComponent
											key={group.id}
											group={group}
											onUpdate={(updated) => updateGroup(index, updated)}
											onDelete={() => deleteGroup(index)}
											root={true}
										/>
										<Operator />
									</div>
								))}
								<AddAnotherFilter onChange={addGroup} />
							</div>
							<div className="mt-10 text-lg">
								Display results as
								<select className="w-full sm:w-fit sm:ml-5 sm:min-w-[200px] bg-gray-200 h-[50px] rounded-[5px] outline-0 px-2 sm:px-3 lg:px-5"
									value={displayAs}
									onChange={e => setDisplayAs(e.target.value)}
								>
									<option value="incident">Incidents</option>
									<option value="participant">Participants</option>
								</select>
							</div>
							<div className="mt-10">
								{/* Search button */}
								<button type="button" onClick={fetcher.state === 'submitting' ? null : handleSubmit} className="rounded-full bg-orange-500 px-5 py-2 text-lg text-white">
									Search
								</button>
							</div>
						</div>

						{fetcher.state === 'submitting' && (
							<div className="my-5 text-gray-600">Searching...</div>
						)}

						{/* Results table */}
						{results.length > 0 && (
							<div>
								<div className="my-5 text-gray-600">
									Found {totalCount} results. Showing page {searchState.page} of {Math.ceil(totalCount / searchState.perPage)}.
								</div>
								<table className="table-gray w-full table-auto text-sm">
									<thead>
										<tr>
											{Object.keys(results[0]).map(key => (
												<th key={key} className="px-6 py-3 text-left text-xs uppercase tracking-wider"
												>
													{key.replace(/_/g, ' ')}
												</th>
											))}
										</tr>
									</thead>
									<tbody>
										{results.map((result, index) => (
											<tr key={index} className="hover:bg-gray-50">
												{Object.values(result).map((value: any, i) => (
													<td key={i} className="px-6 py-4 whitespace-nowrap">
														{value?.toString() || ''}
													</td>
												))}
											</tr>
										))}
									</tbody>
								</table>
								<div className="mt-10 flex justify-center text-sm">
									<Pagination
										total={totalCount}
										current={searchState.page}
										defaultPageSize={searchState.perPage}
										onPageChange={onPageChange}
									/>
								</div>
							</div>
						)}
					</div>
				</main>
				<Footer />
			</div>
		</SearchProvider>
	);
}
