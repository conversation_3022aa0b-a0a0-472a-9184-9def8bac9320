import { useEffect, useRef, useState } from 'react';
import type { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { redirect } from '@remix-run/node';
import { Form, useLoaderData, isRouteErrorResponse, useRouteError } from '@remix-run/react';
import { Link, Element, scrollSpy } from 'react-scroll';
import invariant from 'tiny-invariant';
import qs from 'qs';
import _ from 'lodash';
import moment from '~/utils/moment';
import { authenticator } from '~/utils/auth.server';
import { formSubmitMessage } from '~/utils/cookies.server';
import { getSignedUrl } from '~/utils/s3.server';
import { hasPermission } from '~/utils/client-utils';
import { getTaxonomyDataByVocabulary, type Taxonomy } from '~/models/taxonomy.server';
import { createIncident, getIncident, updateIncident } from '~/models/incidents.server';
import type {
	IncidentValidator,
	IncidentFormType,
	IncidentGun,
	IncidentParticipant,
	IncidentType,
	IncidentLocationCategory
} from '~/models/incidents.server';

import IncidentCharacteristicsField from '~/components/admin/incident/CharacteristicsField';
import IncidentGunsField from '~/components/admin/incident/GunsField';
import IncidentLocationField from '~/components/admin/incident/LocationField';
import IncidentSourcesField from '~/components/admin/incident/SourcesField';
import IncidentValidatedField from '~/components/admin/incident/ValidatedField';
import IncidentParticipantsField from '~/components/admin/incident/ParticipantsField';
import IncidentDistrictsField from '~/components/admin/incident/DistrictsField';
import IncidentGeolocationField from '~/components/admin/incident/GeolocationField';

// Utility function to parse integer with error handling
const parseId = (idStr: string | undefined): number | null => {
	if (!idStr) return null;
	const parsedId = parseInt(idStr, 10);
	return isNaN(parsedId) ? null : parsedId;
};

interface ILoaderData {
	incident: IncidentFormType;
	stateOptions: { id: number; name: string }[];
	locationCategoryOptions: { id: number; name: string }[];
	validatedOptions: { id: number; name: string }[];
	participantRelationshipOptions: { id: number; name: string }[];
	participantStatusOptions: { id: number; name: string }[];
	participantCharacteristicOptions: { id: number; name: string }[];
	participantAgeGroupOptions: { id: number; name: string; minimum_age?: number; maximum_age?: number }[];
	taxonomyIncidentTypes: Taxonomy[];
	taxonomyGunTypes: Taxonomy[];
	googleApiKey: string;
}

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
	invariant(params.id, 'ID must be included in the route params.');

	const taxonomyGunTypes = await getTaxonomyDataByVocabulary('gun_types');

	let incident = {} as IncidentFormType;
	if (params.id === 'new') {
		incident.validators = [];
		incident.participants = [];
		incident.guns = [
			{
				key: 1,
				type_id_1: undefined,
				type_id_2: undefined,
				stolen_value: 'unknown',
				guns_entity_id: 0,
				incident_id: null,
				weight: null,
				gun_type_tid: null,
				changed_date: null
			}
		];
		incident.characteristics = [];
		incident.sources = [
			{
				key: 1,
				source_name: '',
				source_url: '',
				image_fid: null,
				image_width: null,
				image_height: null,
				source_entity_id: 0,
				incident_id: null,
				weight: null,
				changed_date: null,
				image_uri: null,
				image_title: null,
				image_alt: null
			}
		];
		incident.location_categories = [];
	} else {
		const id = parseId(params.id);
		if (id === null) {
			throw new Response('Invalid incident ID', { status: 400 });
		}

		const incidentData = await getIncident(id);
		if (!incidentData) {
			throw new Response('Incident not found', { status: 404 });
		}
		// convert to form type
		incident = _.omit(incidentData, [
			'incident_validators',
			'incident_participants',
			'incident_types',
			'incident_guns',
			'incident_sources',
			'incident_location_categories'
		]) as unknown as IncidentFormType;
		incident.validators = incidentData.incident_validators.map((t: IncidentValidator) => t.validator_tid);
		incident.characteristics = incidentData.incident_types.map((t: IncidentType) => t.type_tid);
		incident.guns = incidentData.incident_guns.map((gun: IncidentGun, index: number) => {
			if (gun.gun_type_tid) {
				const gunType = taxonomyGunTypes.find((t: Taxonomy) => t.tid === gun.gun_type_tid);
				if (gunType?.parent) {
					return { ...gun, key: index + 1, type_id_1: gunType.parent, type_id_2: gun.gun_type_tid };
				} else {
					return { ...gun, key: index + 1, type_id_1: gun.gun_type_tid, type_id_2: undefined };
				}
			}
			return { ...gun, key: index + 1, type_id_1: undefined, type_id_2: undefined };
		});
		incident.sources = incidentData.incident_sources.map((source: any, index: number) => {
			const newSource = { ...source, key: index + 1 };
			if (source.imageFile) {
				// Create a new imageFile object with the required properties
				newSource.imageFile = { ...source.imageFile };
				delete newSource.imageFile.filesize; // JSON.stringify: Do not know how to serialize a BigInt
				newSource.imageFile.preview = getSignedUrl(source.imageFile.uri);
			}
			return newSource;
		});
		incident.participants = incidentData.incident_participants.map(
			(participant: IncidentParticipant, index: number) => {
				return {
					...participant,
					key: index + 1,
					participant_status_tid: participant.participant_status_tid
						? participant.participant_status_tid.split(',')
						: []
				};
			}
		);
		incident.location_categories = incidentData.incident_location_categories.map(
			(t: IncidentLocationCategory) => t.category_tid
		);
	}

	const stateOptions = (await getTaxonomyDataByVocabulary('states')).map((v: Taxonomy) => {
		return { id: v.tid, name: v.value };
	});
	const participantRelationshipOptions = (await getTaxonomyDataByVocabulary('participant_relationship')).map(
		(v: Taxonomy) => {
			return { id: v.tid, name: v.value };
		}
	);
	const participantStatusOptions = (await getTaxonomyDataByVocabulary('participant_statuses')).map((v: Taxonomy) => {
		return { id: v.tid, name: v.value };
	});
	const participantCharacteristicOptions = (await getTaxonomyDataByVocabulary('participant_characteristics')).map(
		(v: Taxonomy) => {
			return { id: v.tid, name: v.value };
		}
	);
	const participantAgeGroupOptions = (await getTaxonomyDataByVocabulary('age_groups')).map((v: Taxonomy) => {
		return { id: v.tid, name: v.value };
	});
	const taxonomyIncidentTypes = await getTaxonomyDataByVocabulary('incident_types');
	const validatedOptions = (await getTaxonomyDataByVocabulary('validators')).map((v: Taxonomy) => {
		return { id: v.tid, name: v.value };
	});
	const locationCategoryOptions = (await getTaxonomyDataByVocabulary('location_categories')).map((v: Taxonomy) => {
		return { id: v.tid, name: v.value };
	});
	const data: ILoaderData = {
		incident,
		stateOptions,
		participantStatusOptions,
		participantCharacteristicOptions,
		participantAgeGroupOptions,
		participantRelationshipOptions,
		taxonomyIncidentTypes,
		taxonomyGunTypes,
		validatedOptions,
		locationCategoryOptions,
		googleApiKey: '' // API key should be used server-side only
	};
	return data;
};

export const action = async ({ params, request }: ActionFunctionArgs) => {
	const user = await authenticator.isAuthenticated(request, { failureRedirect: '/user/login' });

	const cookieHeader = request.headers.get('Cookie');
	const cookie = (await formSubmitMessage.parse(cookieHeader)) || {};

	// Special handling for creating a new incident
	const isCreatingNew = params.id === 'new';
	let id = 0;

	if (!isCreatingNew) {
		id = parseInt(params.id!);
		if (isNaN(id)) {
			throw new Response('Invalid incident ID', { status: 400 });
		}
	}

	// const formData = await request.formData();
	// const updates = Object.fromEntries(formData);
	const formQS = await request.text();
	let updates = qs.parse(formQS);
	// console.log('updates........', updates);
	if (updates.google_auto_complete && !updates.gelocation_override) {
		updates.latitude = updates.google_latitude;
		updates.longitude = updates.google_longitude;
	}

	const incident = { ...updates, author_uid: user.id, last_changed_by_uid: user.id } as IncidentFormType;
	try {
		if (isCreatingNew) {
			await createIncident(incident);
		} else {
			await updateIncident(id, incident);
		}
	} catch (error: any) {
		console.error(
			`Error ${isCreatingNew ? 'creating' : 'updating'} incident${!isCreatingNew ? ` (ID: ${id})` : ''}:`,
			error
		);
		throw new Error('Unable to insert or update the database. Check your inputs to make sure they are valid.');
	}

	cookie.message = 'The incident was saved successfully!';
	return redirect(`/admin/incident`, {
		headers: {
			'Set-Cookie': await formSubmitMessage.serialize(cookie)
		}
	});
};

export default function IncidentFormPage() {
	const data = useLoaderData<typeof loader>();
	const { incident } = data;

	const inputRef = useRef<HTMLInputElement>(null);
	const [inputWidth, setInputWidth] = useState(0);
	const [notes, setNotes] = useState(incident.notes || '');

	useEffect(() => {
		setInputWidth(inputRef.current?.offsetWidth || 0);
		// Updating scrollSpy when the component mounts.
		scrollSpy.update();
	}, []);

	const disableSubmit = false; // TODO
	return (
		<main className="admin incident">
			<Form method="post">
				<div className="tabbar sticky top-[60px] z-40">
					<div className="container mx-auto flex items-center justify-between">
						<ul className="tabbar-nav">
							<li>
								<Link
									activeClass="active"
									className="link"
									to="stage-information"
									spy={true}
									smooth={false}
									isDynamic={true}
									duration={500}
									offset={-160}
									hashSpy={true}
									spyThrottle={500}
								>
									Information
								</Link>
							</li>
							<li>
								<Link
									activeClass="active"
									className="link"
									to="stage-location"
									spy={true}
									smooth={false}
									isDynamic={true}
									duration={500}
									offset={-160}
									hashSpy={true}
									spyThrottle={500}
								>
									Location
								</Link>
							</li>
							<li>
								<Link
									activeClass="active"
									className="link"
									to="stage-participants"
									spy={true}
									smooth={false}
									isDynamic={true}
									duration={500}
									offset={-160}
									hashSpy={true}
									spyThrottle={500}
								>
									Participants
								</Link>
							</li>
							<li>
								<Link
									activeClass="active"
									className="link"
									to="stage-characteristics"
									spy={true}
									smooth={false}
									isDynamic={true}
									duration={500}
									offset={-160}
									hashSpy={true}
									spyThrottle={500}
								>
									Characteristics
								</Link>
							</li>
							<li>
								<Link
									activeClass="active"
									className="link"
									to="stage-notes"
									spy={true}
									smooth={false}
									isDynamic={true}
									duration={500}
									offset={-160}
									hashSpy={true}
									spyThrottle={500}
								>
									Notes
								</Link>
							</li>
							<li>
								<Link
									activeClass="active"
									className="link"
									to="stage-guns"
									spy={true}
									smooth={false}
									isDynamic={true}
									duration={500}
									offset={-160}
									hashSpy={true}
									spyThrottle={500}
								>
									Gun
								</Link>
							</li>
							<li>
								<Link
									activeClass="active"
									className="link"
									to="stage-sources"
									spy={true}
									smooth={false}
									isDynamic={true}
									duration={500}
									offset={-160}
									hashSpy={true}
									spyThrottle={500}
								>
									Sources
								</Link>
							</li>
							<li>
								<Link
									activeClass="active"
									className="link"
									to="stage-misc"
									spy={true}
									smooth={false}
									isDynamic={true}
									duration={500}
									offset={-160}
									hashSpy={true}
									spyThrottle={500}
								>
									Misc
								</Link>
							</li>
						</ul>
						<div>
							<button
								type="submit"
								className={`rounded-full bg-orange-500 px-5 py-1.5 text-lg text-white${disableSubmit ? ' opacity-25' : ''}`}
								disabled={disableSubmit}
							>
								Submit
							</button>
						</div>
					</div>
				</div>
				<div className="container mx-auto">
					<Element name="stage-information">
						<div className="pt-10">
							<div className="section">
								<div className="title">Date & Time</div>
								<div className="grid grid-cols-4 gap-10">
									<div>
										<label
											htmlFor="IncidentDate"
											className="text-lg font-bold after:ml-0.5 after:text-orange-500 after:content-['*']"
										>
											Incident Date
										</label>
										<div className="mt-3">
											<input
												ref={inputRef}
												id="IncidentDate"
												name="incident_date"
												type="date"
												required={true}
												defaultValue={
													incident.incident_date
														? moment.unix(incident.incident_date).format('YYYY-MM-DD')
														: moment().format('YYYY-MM-DD')
												}
												className="block w-full"
											/>
										</div>
									</div>
									<div>
										<label htmlFor="IncidentTime" className="text-lg font-bold">
											Incident Time
										</label>
										<div className="mt-3">
											<input
												id="IncidentTime"
												name="incident_time"
												type="text"
												defaultValue={
													incident.incident_time
														? moment.unix(incident.incident_time).format('h:mm A')
														: ''
												}
												autoComplete="off"
												className="block w-full time-picker-single"
											/>
										</div>
									</div>
									<div className="col-span-2">
										<label>&nbsp;</label>
										<div className="mt-3 flex h-[50px] items-center">
											<input
												id="ApproximateTime"
												name="approximate_time"
												type="checkbox"
												value={1}
												defaultChecked={!!incident.approximate_time}
												className="mr-4"
											/>
											<label htmlFor="ApproximateTime" className="text-lg">
												Approximate Time
											</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</Element>

					<Element name="stage-location">
						<div className="pt-10">
							<div className="section">
								<div className="title">Incident Location</div>
								<IncidentLocationField
									location={{
										address: incident.address || '',
										state: incident.state_taxonomy_id || undefined,
										city: incident.city_county || '',
										county: incident.county || '',
										neighborhood: incident.neighborhood || '',
										latitude: incident.latitude || undefined,
										longitude: incident.longitude || undefined,
										zipcode: incident.zipcode || '',
										business: incident.business || '',
										categories: incident.location_categories,
										locked: !!incident.google_auto_complete
									}}
									dropOff={!!incident.drop_off}
									googleApiKey={data.googleApiKey}
									stateOptions={data.stateOptions}
									locationCategoryOptions={data.locationCategoryOptions}
									inputWidth={inputWidth}
								/>
							</div>
						</div>
					</Element>

					<Element name="stage-participants">
						<div className="pt-10">
							<IncidentParticipantsField participants={incident.participants as any} />
						</div>
					</Element>

					<Element name="stage-characteristics">
						<div className="pt-10">
							<IncidentCharacteristicsField characteristicIds={incident.characteristics} />
						</div>
					</Element>

					<Element name="stage-notes">
						<div className="pt-10">
							<div className="section">
								<div className="title">Notes</div>
								<div>
									<label htmlFor="PublicNotes" className="text-lg font-bold">
										Public Notes
										<span className="ml-[18px] text-sm font-normal text-gray-600">
											Characters: {notes?.length || 0} of 320
										</span>
									</label>
									<div className="mt-3">
										<textarea
											id="PublicNotes"
											name="notes"
											rows={4}
											maxLength={320}
											value={notes}
											onChange={e => setNotes(e.target.value)}
											className="block w-full rounded-[5px] px-5 py-2.5 text-lg outline-0"
										/>
									</div>
								</div>
								<div className="mt-8">
									<label htmlFor="PrivateNotes" className="text-lg font-bold">
										Private GVA-Only Notes
									</label>
									<div className="mt-3">
										<textarea
											id="PrivateNotes"
											name="private_notes"
											rows={4}
											defaultValue={incident.private_notes || ''}
											className="block w-full rounded-[5px] px-5 py-2.5 text-lg outline-0"
										/>
									</div>
								</div>
								<div className={`mt-8 ${hasPermission('Admin') ? '' : 'hidden'}`}>
									<label htmlFor="IncidentAlias" className="text-lg font-bold">
										Incident Alias
									</label>
									<div className="mt-3">
										<input
											id="IncidentAlias"
											name="incident_alias"
											type="text"
											defaultValue={incident.incident_alias || ''}
											className="block w-full"
										/>
									</div>
								</div>
							</div>
						</div>
					</Element>

					<Element name="stage-guns">
						<div className="pt-10">
							<div className="section">
								<div className="title">Gun</div>
								<IncidentGunsField guns={incident.guns as any} />
							</div>
						</div>
					</Element>

					<Element name="stage-sources">
						<div className="pt-10">
							<div className="section">
								<div className="title">Source Information</div>
								<IncidentSourcesField sources={incident.sources as any} />
							</div>
						</div>
					</Element>

					<Element name="stage-misc">
						<div className="pt-10" style={{ paddingBottom: 500 }}>
							<div className="section">
								<div className="title">Geolocation Data</div>
								<div>
									<label htmlFor="GeocodeNotes" className="text-lg font-bold">
										Geocode Notes
									</label>
									<div className="mt-3">
										<input
											id="GeocodeNotes"
											name="geocode_notes"
											type="text"
											defaultValue={incident.geocode_notes || ''}
											className="block w-full"
										/>
									</div>
								</div>
								<div className="mt-8">
									<label className="text-lg font-bold">Overrides</label>
									<div className="mt-3">
										<IncidentGeolocationField
											override={!!incident.gelocation_override}
											latitude={incident.latitude || undefined}
											longitude={incident.longitude || undefined}
										/>
									</div>
									<div className="mt-5">
										<IncidentDistrictsField
											override={!!incident.districts_override}
											congressionalDistrict={incident.congressional_district || ''}
											stateSenateDistrict={incident.state_senate_district || ''}
											stateHouseDistrict={incident.state_house_district || ''}
										/>
									</div>
								</div>
							</div>

							<div className="section mt-10">
								<div className="title">Related Incidents</div>
								<div>
									<label htmlFor="RelatedIncidents" className="text-lg font-bold">
										List Incident ID Numbers as a Comma Separated List
									</label>
									<div className="mt-3">
										<input
											id="RelatedIncidents"
											name="related"
											type="text"
											defaultValue={incident.related || ''}
											className="block w-full"
										/>
									</div>
								</div>
							</div>

							{hasPermission('Admin') && (
								<div className="section mt-10">
									<div className="title">Incident Validated</div>
									<IncidentValidatedField
										options={data.validatedOptions}
										validators={incident.validators}
									/>
								</div>
							)}
						</div>
					</Element>
				</div>
			</Form>
		</main>
	);
}

export function ErrorBoundary() {
	const error = useRouteError();

	if (error instanceof Error) {
		return <div>An unexpected error occurred: {error.message}</div>;
	}

	if (!isRouteErrorResponse(error)) {
		return <h1>Unknown Error</h1>;
	}

	if (error.status === 404) {
		return <div>Incident not found</div>;
	}

	return <div>An unexpected error occurred: {error.statusText}</div>;
}
