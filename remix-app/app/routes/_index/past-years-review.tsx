// @ts-ignore
import logo from '~/images/Gun-Violence-Archive-Logo-Reverse.svg';
import { getConfigValueByKey } from '~/models/config.server';
import { YEARREVIEW10_FOOTER_KEY } from '~/models/toll-source-modifications';
import { preprocessReview } from "~/services/toll/toll.server";
import moment from "moment-timezone";
import { TollYearRange } from "~/services/toll/TollItem.server";

export const getPastYearsData = async () => {
  const yearStart = moment().subtract(11, 'years').format('YYYY');
  const yearEnd = moment().subtract(1, 'year').format('YYYY');
  const data = { year_range: yearStart.toString() + '-' + yearEnd.toString(), past_toll: true };
  await preprocessReview(data, []);

  const yearReviewFooter = await getConfigValueByKey(YEARREVIEW10_FOOTER_KEY);
  return { values: data, footer: yearReviewFooter?.content || '' };
};

export const PastYearsReview = ({ data }: { data: { values: any; footer: string } }) => {

  return (
    <div className="rounded-[10px] bg-blue-500 py-9 text-white">
      <table className="table-blue w-full text-sm">
        <thead>
          <tr className="text-[25px] font-bold">
            <th align="left" className="pb-4 pl-9">
              GVA 10-Year Review
            </th>
            {data.values.year_range.map((year: TollYearRange, index: any, row: any) => (
              <th key={`past-review-col-${index}`} align="right" className={"pb-4 " + ((index + 1 === row.length) ? 'pr-9' : '')}>
                {year.str}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {Object.values(data.values.values).map(
            (
              item: {
                info: { label: string };
                years: Array<{ value: string | number }>;
              },
              index: number
            ) => (
              <tr key={`past-review-row-${index}`}>
                <td align="left" className="py-2.5 pl-9">
                  <div
                    dangerouslySetInnerHTML={{
                      __html: item.info.label
                    }}
                  ></div>
                </td>
                {item.years.map((year, index: number, row: Array<{ value: string | number }>) => (
                  <td key={`past-review-val-${index}`} align='right' className={((index + 1 === row.length) ? 'pr-9' : '')}>
                    {year.value}
                  </td>
                ))}
              </tr>
            )
          )}
        </tbody>
      </table>

      <div className="mt-9 px-9 text-xs">
        <div dangerouslySetInnerHTML={{ __html: data.footer }}></div>
        <div className="mt-4">© 2013-{new Date().getFullYear()}</div>
        <img className="mt-4" src={logo} alt="Gun Violence Archive" width={230} />
      </div>
    </div>
  );
};
