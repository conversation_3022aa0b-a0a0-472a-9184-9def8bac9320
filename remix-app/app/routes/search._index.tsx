import { Form, Link, useFetcher, useLoaderData } from '@remix-run/react';
import { Header } from '~/components/common/Header';
import { Footer } from '~/components/common/Footer';

import vector from '~/images/Vector 2.svg';
import AddAnotherFilter from '~/components/search/AddAnotherFilter';
import { SearchProvider } from '~/context/SearchContext';
import { FilterRegistry } from '~/services/search';
import { useEffect, useState } from 'react';
import type { Filter, SearchState } from '~/types/search';
import FilterComponent from '~/components/search/FilterComponent2';
import Pagination from '~/components/Pagination';

export const loader = async () => {
	return {};
};

export default function Search() {
	// Get all available filters
	const availableFilters = FilterRegistry.getInstance().getAllAsMap();

	const fetcher = useFetcher();

	const [searchState, setSearchState] = useState<SearchState>({
		groups: [{ id: 'root', type: 'group', operator: 'AND', filters: [] }],
		page: 1,
		perPage: 20
	});

	const [displayAs, setDisplayAs] = useState('incident');

	const [results, setResults] = useState([]);
	const [totalCount, setTotalCount] = useState(0);

	const addFilter = (field: string, operator: string, value: any) => {
		const newFilters = [...searchState.groups[0].filters, {
			id: Date.now().toString(),
			type: 'filter',
			field,
			operator,
			value
		}];
		const group = { ...searchState.groups[0], filters: newFilters };
		setSearchState(prev => ({
			...prev,
			groups: [group]
		}));
	};

	const updateFilter = (index: number, updatedFilter: Filter) => {
		const newFilters = [...searchState.groups[0].filters];
		newFilters[index] = updatedFilter;
		const group = { ...searchState.groups[0], filters: newFilters };
		console.log('group-------------', group);
		setSearchState(prev => ({
			...prev,
			groups: [group]
		}));
	};

	const deleteFilter = (index: number) => {
		const group = {
			...searchState.groups[0],
			filters: searchState.groups[0].filters.filter((_, i) => i !== index)
		};
		setSearchState(prev => ({
			...prev,
			groups: [group]
		}));
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		if (searchState.groups[0].filters.length === 0) {
			return;
		}
		setSearchState(prev => ({ ...prev, page: 1 }));
		onSearch({ ...searchState, page: 1 });
	};

	const onSearch = (search: SearchState) => {
		fetcher.submit(
			// @ts-ignore
			{
				baseTable: displayAs === 'incident' ? 'gva_data.incidents' : 'gva_data.incident_participants',
				baseAlias: 'inc',
				filterGroups: search.groups,
				page: search.page,
				pageSize: search.perPage,
				columns: displayAs === 'incident' ? ['incident_id', 'incident_date', 'state', 'city', 'address'] : ['participant_id', 'name'],
				format: 'array',
				userRoles: ['user']
			},
			{ method: 'post', action: '/api/search', encType: 'application/json' }
		);
	};

	const onPageChange = (page: number) => {
		setSearchState(prev => ({ ...prev, page }));
		onSearch({ ...searchState, page });
	};

	useEffect(() => {
		if (fetcher.data) {
			console.log('Fetcher data:', fetcher.data);
			setResults(fetcher.data.data);
			setTotalCount(fetcher.data.totalCount);
		}
	}, [fetcher.data]);

	return (
		<SearchProvider filters={availableFilters}>
			<div className="min-h-screen">
				<Header />
				<main className="py-10 md:py-20">
					<div className="container mx-auto">
						{/* Search form */}
						<div>
							<div className="querybuilder basic">
								{searchState.groups[0].filters.map((filter, index) => (
									<div key={filter.id}>
										<FilterComponent
											filter={filter}
											onUpdate={updated => updateFilter(index, updated)}
											onDelete={() => deleteFilter(index)}
											className={index > 0 ? 'mt-[-60px]' : ''}
										/>
										<AndOrOperator />
									</div>
								))}
								<AddAnotherFilter className={searchState.groups[0].filters.length > 0 ? 'mt-[-60px]' : 'ml-[-30px]'} onChange={addFilter} />
							</div>
							<div className="mt-10 text-lg">
								Display results as
								<select className="w-full sm:w-fit sm:ml-5 sm:min-w-[200px] bg-gray-200 h-[50px] rounded-[5px] outline-0 px-2 sm:px-3 lg:px-5"
									value={displayAs}
									onChange={e => setDisplayAs(e.target.value)}
								>
									<option value="incident">Incidents</option>
									<option value="participant">Participants</option>
								</select>
							</div>
							<div className="mt-10">
								{/* Search button */}
								<button type="button" onClick={fetcher.state === 'submitting' ? null : handleSubmit} className="rounded-full bg-orange-500 px-5 py-2 text-lg text-white">
									Search
								</button>
							</div>
						</div>

						{fetcher.state === 'submitting' && (
							<div className="my-5 text-gray-600">Searching...</div>
						)}

						{/* Results table */}
						{results.length > 0 && (
							<div>
								<div className="my-5 text-gray-600">
									Found {totalCount} results. Showing page {searchState.page} of {Math.ceil(totalCount / searchState.perPage)}.
								</div>
								<table className="table-gray w-full table-auto text-sm">
									<thead>
										<tr>
											{Object.keys(results[0]).map(key => (
												<th key={key} className="px-6 py-3 text-left text-xs uppercase tracking-wider"
												>
													{key.replace(/_/g, ' ')}
												</th>
											))}
										</tr>
									</thead>
									<tbody>
										{results.map((result, index) => (
											<tr key={index} className="hover:bg-gray-50">
												{Object.values(result).map((value: any, i) => (
													<td key={i} className="px-6 py-4 whitespace-nowrap">
														{value?.toString() || ''}
													</td>
												))}
											</tr>
										))}
									</tbody>
								</table>
								<div className="mt-10 flex justify-center text-sm">
									<Pagination
										total={totalCount}
										current={searchState.page}
										defaultPageSize={searchState.perPage}
										onPageChange={onPageChange}
									/>
								</div>
							</div>
						)}
					</div>
				</main>
				<Footer />
			</div>
		</SearchProvider>
	);
}

const AndOrOperator = ({ or = false }: { or?: boolean }) => {
	return (
		<div className="operator">
			<img src={vector} alt="" width={25} />
			<div className="andor">{or ? 'OR' : 'AND'}</div>
		</div>
	);
};
