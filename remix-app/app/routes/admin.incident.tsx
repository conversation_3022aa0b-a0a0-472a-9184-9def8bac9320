import type { LinksFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';

import { AdminHeader } from '~/components/admin/Header';
import { useUser } from '~/utils/client-utils';

export const links: LinksFunction = () => [
	{ rel: 'stylesheet', href: 'http://cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.css' }
];

export default function AdminIncidentsPage() {
	const user = useUser();
	if (user.role != 'Admin' && user.role != 'Editor') {
		throw new Error('No permission.');
	}
	return (
		<div className="min-h-screen bg-white">
			<AdminHeader />

			<Outlet />
			<script src="http://cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.js"></script>
			<script src="/scripts/admin.incident.js"></script>
		</div>
	);
}
