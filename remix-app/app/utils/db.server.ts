import type { Prisma } from '@prisma/client';
import { PrismaClient } from '@prisma/client';
import { withOptimize } from '@prisma/extension-optimize';
import * as process from 'node:process';
import { createPrismaRedisCache } from 'prisma-redis-middleware';
import { redisManager } from '~/utils/redis-connection.server';
import superjson from 'superjson';

const redisCache = redisManager.getClient();
const redis = redisCache.getNativeClient();
let db = new PrismaClient();

const cacheMiddleware: Prisma.Middleware = createPrismaRedisCache({
	storage: { type: 'redis', options: { client: redis, invalidation: { referencesTTL: 14400 } } },
	cacheTime: 14400,
	transformer: {
		serialize: result => superjson.serialize(result),
		deserialize: serialized => superjson.deserialize(serialized)
	}
});

// If we are in development mode, we want to use the Optimize extension
if (process.env.NODE_ENV !== 'production') {
	if (process.env.OPTIMIZE_API_KEY) {
		// Create a new instance with the Optimize extension
		db.$extends(withOptimize({ apiKey: process.env.OPTIMIZE_API_KEY }));
	}
}

db.$use(cacheMiddleware);

export { db };
