main.admin input[readonly] {
    cursor: default;
    background-color: rgba(239, 239, 239, 0.3);
}

main.admin.incident .section {
    @apply bg-gray-200 rounded-[5px] p-10
}

main.admin.incident .section .title {
    @apply font-bold text-[25px] leading-8 text-orange-500 mb-6;
}

main.admin.incident input[type=checkbox] {
    @apply h-[30px] w-[30px] accent-orange-500
}

main.admin.incident input[type=file] {
    @apply h-auto px-0
}

main.admin.incident textarea[name=notes] {
    background-color: rgba(254, 206, 206, 1);
}

main.admin.incident textarea[name=private_notes] {
    background-color: rgba(207, 255, 224, 1);
}

main.admin.incident .react-time-picker__wrapper {
    border: none;
    background: #ffffff;
    border-radius: 5px;
}

main.admin.incident .react-time-picker__button {
    padding-right: 1.25rem;
}

main.admin.incident .react-time-picker__inputGroup__input:invalid {
    background: #ffffff;
}

.column-selector .item {
    appearance: none;
    background: #FFF;
    padding: 10px 20px;
    border: none;
    cursor: grab;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    transform: scale(1);
    box-shadow: inset 0px 0px 1px rgba(0, 0, 0, 0.4), 0 0 0 calc(1px / var(--scale-x, 1)) rgba(63, 63, 68, 0.05), 0px 1px calc(2px / var(--scale-x, 1)) 0 rgba(34, 33, 81, 0.05);
}

.column-selector .item[data-dragging="true"] {
    transform: scale(1.02);
    box-shadow: inset 0px 0px 1px rgba(0, 0, 0, 0.5), -1px 0 15px 0 rgba(34, 33, 81, 0.01), 0px 15px 15px 0 rgba(34, 33, 81, 0.25)
}