import { useState, useEffect } from 'react';
import { Link, useSearchParams } from '@remix-run/react';
import Pagination from 'rc-pagination';

export default ({ total, current, defaultPageSize, onPageChange }: { total: number; current?: number; defaultPageSize: number; onPageChange?: Function }) => {
	const [searchParams] = useSearchParams();
	const [isMounted, setIsMounted] = useState(false);

	useEffect(() => {
		setIsMounted(true);
	}, []);

	if (!isMounted) return null;

	if (onPageChange) {
		return (
			<Pagination
				className="pagination"
				total={total}
				current={current}
				defaultPageSize={defaultPageSize}
				showTitle={false}
				itemRender={(current, type, element) => {
					if (type === 'prev') {
						return (
							<button type="button" onClick={() => onPageChange(current)}>
								&lt; PREV
							</button>
						);
					}
					if (type === 'next') {
						return (
							<button type="button" onClick={() => onPageChange(current)}>
								NEXT &gt;
							</button>
						);
					}
					if (type === 'page') {
						return (
							<button type="button" onClick={() => onPageChange(current)}>
								{type === 'page' && current}
							</button>
						);
					}
					if (type === 'jump-prev' || type === 'jump-next') {
						return (
							<button type="button" onClick={() => onPageChange(current)}>
								...
							</button>
						);
					}
					return element;
				}}
				showSizeChanger={false}
			/>
		);
	}
	return (
		<Pagination
			className="pagination"
			total={total}
			defaultPageSize={defaultPageSize}
			showTitle={false}
			itemRender={(current, type, element) => {
				if (type === 'prev') {
					return (
						<Link
							to={{ search: setSearchParamsString(searchParams, { page: current }) }}
							preventScrollReset
						>
							&lt; PREV
						</Link>
					);
				}
				if (type === 'next') {
					return (
						<Link
							to={{ search: setSearchParamsString(searchParams, { page: current }) }}
							preventScrollReset
						>
							NEXT &gt;
						</Link>
					);
				}
				if (type === 'page') {
					return (
						<Link
							to={{ search: setSearchParamsString(searchParams, { page: current }) }}
							preventScrollReset
						>
							{type === 'page' && current}
						</Link>
					);
				}
				if (type === 'jump-prev' || type === 'jump-next') {
					return (
						<Link
							to={{ search: setSearchParamsString(searchParams, { page: current }) }}
							preventScrollReset
						>
							...
						</Link>
					);
				}
				return element;
			}}
			showSizeChanger={false}
		/>
	);
};

const setSearchParamsString = (searchParams: URLSearchParams, changes: Record<string, string | number | undefined>) => {
	const newSearchParams = new URLSearchParams(searchParams);
	for (const [key, value] of Object.entries(changes)) {
		if (value === undefined) {
			newSearchParams.delete(key);
			continue;
		}
		newSearchParams.set(key, String(value));
	}

	return Array.from(newSearchParams.entries())
		.map(([key, value]) => (value ? `${key}=${encodeURIComponent(value)}` : key))
		.join('&');
};
