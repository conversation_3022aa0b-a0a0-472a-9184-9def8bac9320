import type { Filter, FilterGroup } from '~/types/search';
import { FilterComponent } from './FilterComponent';

interface FilterGroupComponentProps {
  group: FilterGroup;
  onUpdate: (group: FilterGroup) => void;
  onDelete: () => void;
}

export function FilterGroupComponent({ group, onUpdate, onDelete }: FilterGroupComponentProps) {
  const addFilter = () => {
    onUpdate({
      ...group,
      filters: [...group.filters, {
        id: Date.now().toString(),
        type: 'filter',
        field: '',
        operator: 'equals',
        value: ''
      }]
    });
  };

  const addNestedGroup = () => {
    onUpdate({
      ...group,
      filters: [...group.filters, {
        id: Date.now().toString(),
        type: 'group',
        operator: 'AND',
        filters: []
      }]
    });
  };

  const updateFilter = (index: number, updatedFilter: Filter | FilterGroup) => {
    const newFilters = [...group.filters];
    newFilters[index] = updatedFilter;
    onUpdate({ ...group, filters: newFilters });
  };

  const deleteFilter = (index: number) => {
    onUpdate({
      ...group,
      filters: group.filters.filter((_, i) => i !== index)
    });
  };

  return (
    <div className="rounded border p-4">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <select
            value={group.operator}
            onChange={(e) => onUpdate({ ...group, operator: e.target.value as 'AND' | 'OR' })}
            className="rounded border"
          >
            <option value="AND">AND</option>
            <option value="OR">OR</option>
          </select>
          <span className="text-sm text-gray-600">Match {group.operator.toLowerCase()} conditions</span>
        </div>
        <button
          type="button"
          onClick={onDelete}
          className="text-red-500 hover:text-red-700"
        >
          Remove Group
        </button>
      </div>

      <div className="space-y-2">
        {group.filters.map((filter, index) => (
          <div key={filter.id} className="pl-4">
            {filter.type === 'group' ? (
              <FilterGroupComponent
                group={filter as FilterGroup}
                onUpdate={(updated) => updateFilter(index, updated)}
                onDelete={() => deleteFilter(index)}
              />
            ) : (
              <FilterComponent
                filter={filter}
                onUpdate={(updated) => updateFilter(index, updated)}
                onDelete={() => deleteFilter(index)}
              />
            )}
          </div>
        ))}
      </div>

      <div className="mt-4 flex gap-2">
        <button
          type="button"
          onClick={addFilter}
          className="rounded border px-3 py-1 text-sm hover:bg-gray-50"
        >
          Add Filter
        </button>
        <button
          type="button"
          onClick={addNestedGroup}
          className="rounded border px-3 py-1 text-sm hover:bg-gray-50"
        >
          Add Nested Group
        </button>
      </div>
    </div>
  );
}