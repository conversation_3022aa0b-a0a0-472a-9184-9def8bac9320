import { useSearchContext } from '~/context/SearchContext';
import type { QueryOperators } from '~/services/query/types';

export default ({ onChange, className = '' }: { onChange: Function; className?: string }) => {
	const { filters } = useSearchContext();

	const handleFieldChange = (fieldId: string) => {
		const newFilterDef = filters.get(fieldId);
		if (!newFilterDef) return;

		const operators = newFilterDef.getOperators();
		if (operators.length === 0) return;

		const defaultOperator = operators[0].value;
		if (!isValidOperator(defaultOperator)) return;

		const defaultValue = newFilterDef.getDefaultValue(defaultOperator);
		onChange(fieldId, defaultOperator, defaultValue);
	};

	return (
		<div className={`filter ${className}`}>
			<div className="title">Option: Add Another Filter</div>
			<select className="w-full md:w-1/2" onChange={e => handleFieldChange(e.target.value)} value="">
				<option value="">Type of Filter</option>
				{Array.from(filters.entries()).map(([key, f]) => (
					<option key={key} value={key}>{f.name}</option>
				))}
			</select>
		</div>
	);
};

// Type guard to ensure operator is valid
function isValidOperator(operator: string | undefined): operator is QueryOperators {
	if (!operator) return false;

	const validOperators: QueryOperators[] = [
		'equals',
		'not',
		'in',
		'notIn',
		'lt',
		'lte',
		'gt',
		'gte',
		'contains',
		'search',
		'startsWith',
		'endsWith'
	];
	return validOperators.includes(operator as QueryOperators);
}
