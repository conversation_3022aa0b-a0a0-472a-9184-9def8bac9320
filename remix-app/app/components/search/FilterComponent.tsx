import type { Filter } from '~/types/search';
import type { QueryOperators } from '~/services/query/types';
import { useSearchContext } from '~/context/SearchContext';
import { FilterFormProps } from '~/services/search/types';

interface FilterComponentProps {
  filter: Filter;
  onUpdate: (filter: Filter) => void;
  onDelete: () => void;
}

export function FilterComponent({ filter, onUpdate, onDelete }: FilterComponentProps) {
  const { filters } = useSearchContext();
  const filterDef = filters.get(filter.field);

  // if (!filterDef) return null;

  const operators = filterDef?.getOperators();
  const InputComponent = filterDef?.getInputComponent(filter.operator);

  const handleOperatorChange = (operator: string) => {
    if (isValidOperator(operator)) {
      onUpdate({
        ...filter,
        operator,
        value: filterDef.getDefaultValue(operator)
      });
    }
  };

  const handleFieldChange = (fieldId: string) => {
    const newFilterDef = filters.get(fieldId);
    if (!newFilterDef) return;

    const operators = newFilterDef.getOperators();
    if (operators.length === 0) return;

    const defaultOperator = operators[0].value;
    if (!isValidOperator(defaultOperator)) return;

    onUpdate({
      ...filter,
      field: fieldId,
      operator: defaultOperator,
      value: newFilterDef.getDefaultValue(defaultOperator)
    });
  };

  // Check if the filter has a custom form component
  if (filterDef && 'getFormComponent' in filterDef && typeof (filterDef as any).getFormComponent === 'function') {
    const FormComponent = (filterDef as any).getFormComponent();

    const handleSubmit = (value: any) => {
      // Use the filter's custom submit handler if available, otherwise just update the filter
      const processedValue = 'handleSubmit' in filterDef && typeof (filterDef as any).handleSubmit === 'function'
        ? (filterDef as any).handleSubmit(value)
        : value;
      onUpdate({ ...filter, value: processedValue });
    };

    return (
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between mb-2">
          <div className="font-medium">{filterDef.name}</div>
          <button
            type="button"
            onClick={onDelete}
            className="rounded bg-red-500 px-2 py-1 text-sm text-white"
          >
            Delete
          </button>
        </div>

        <FormComponent
          value={filter.value}
          onChange={(value: any) => onUpdate({ ...filter, value })}
          onSubmit={handleSubmit}
          filter={filterDef}
          className="flex-1"
        />
      </div>
    );
  }

  // Default rendering with field, operator, and input
  return (
    <div className="flex items-center gap-2">
      <select
        value={filter.field}
        onChange={(e) => handleFieldChange(e.target.value)}
        className="rounded border p-1"
      >
        <option value="">Select Field</option>
        {Array.from(filters.entries()).map(([key, f]) => (
          <option key={key} value={key}>{f.name}</option>
        ))}
      </select>

      {operators && (
        <select
          value={filter.operator}
          onChange={(e) => handleOperatorChange(e.target.value)}
          className="rounded border p-1"
        >
          {operators.map(op => (
            <option key={op.value} value={op.value}>{op.label}</option>
          ))}
        </select>
      )}

      {InputComponent && (
        <InputComponent
          value={filter.value}
          onChange={(value) => onUpdate({ ...filter, value })}
          operator={filter.operator}
          filter={filterDef}
          className="flex-1"
        />
      )}

      <button
        type="button"
        onClick={onDelete}
        className="rounded bg-red-500 px-2 py-1 text-sm text-white"
      >
        Delete
      </button>
    </div>
  );
}

// Type guard to ensure operator is valid
function isValidOperator(operator: string | undefined): operator is QueryOperators {
  if (!operator) return false;

  const validOperators: QueryOperators[] = [
    'equals',
    'not',
    'in',
    'notIn',
    'lt',
    'lte',
    'gt',
    'gte',
    'contains',
    'search',
    'startsWith',
    'endsWith'
  ];
  return validOperators.includes(operator as QueryOperators);
}
