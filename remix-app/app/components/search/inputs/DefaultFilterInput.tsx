import type { FilterInputProps } from "~/services/search/types";

export function DefaultFilterInput({ value, onChange, operator, filter, className = '' }: FilterInputProps) {
	if (!filter) return null;

	const operators = filter.getOperators();
	const currentOperator = operators.find(op => op.value === operator);

	if (!currentOperator) return null;

	switch (currentOperator.inputType) {
		case 'select':
			return (
				<select
					value={value}
					onChange={(e) => onChange(e.target.value)}
					className={`border ${className}`}
				>
					<option value="">Select...</option>
					{currentOperator.options?.map(option => (
						<option key={option.value} value={option.value}>
							{option.label}
						</option>
					))}
				</select>
			);

		case 'text':
		default:
			return (
				<input
					type="text"
					value={value}
					onChange={(e) => onChange(e.target.value)}
					className={`border ${className}`}
					placeholder="Enter value..."
				/>
			);
	}
}
