import type { Filter, FilterGroup } from '~/types/search';
import FilterComponent from './FilterComponent2';
import Operator from './Operator';
import iconAdd from '~/images/Icon-Admin-Add.svg';
import iconDelete from '~/images/Icon-Admin-Delete.svg';

interface FilterGroupComponentProps {
	group: FilterGroup;
	onUpdate: (group: FilterGroup) => void;
	onDelete: () => void;
	root?: boolean;
}

export default function FilterGroupComponent({ group, onUpdate, onDelete, root }: FilterGroupComponentProps) {
	const addFilter = () => {
		onUpdate({
			...group,
			filters: [...group.filters, {
				id: Date.now().toString(),
				type: 'filter',
				field: '',
				operator: 'equals',
				value: ''
			}]
		});
	};

	const addNestedGroup = () => {
		onUpdate({
			...group,
			filters: [...group.filters, {
				id: Date.now().toString(),
				type: 'group',
				operator: 'AND',
				filters: []
			}]
		});
	};

	const updateFilter = (index: number, updatedFilter: Filter | FilterGroup) => {
		const newFilters = [...group.filters];
		newFilters[index] = updatedFilter;
		onUpdate({ ...group, filters: newFilters });
	};

	const deleteFilter = (index: number) => {
		onUpdate({
			...group,
			filters: group.filters.filter((_, i) => i !== index)
		});
	};

	const insertFilter = (index: number) => {
		const newFilters = [...group.filters];
		newFilters.splice(index, 0, {
			id: Date.now().toString(),
			type: 'filter',
			field: '',
			operator: 'equals',
			value: ''
		});
		onUpdate({ ...group, filters: newFilters });
	};

	if (root && group.filters.length === 1) {
		const firstFilter = group.filters[0];
		if (firstFilter.type === 'filter') {
			return (
				<FilterComponent
					filter={firstFilter}
					onUpdate={(updated) => updateFilter(0, updated)}
					onDelete={onDelete} // delete group
					addFilter={addFilter}
				/>
			);
		}
	}

	return (
		<div className="group">
			<div className="anyall flex items-start justify-between">
				<div>
					<select
						value={group.operator}
						onChange={(e) => onUpdate({ ...group, operator: e.target.value as 'AND' | 'OR' })}>
						<option value="OR">Any</option>
						<option value="AND">All</option>
					</select>of the following are true:
				</div>
				<img src={iconDelete} alt="Delete" width={16} className="cursor-pointer" onClick={onDelete} />
			</div>

			{group.filters.map((filter, index) => (
				<div key={filter.id}>
					{filter.type === 'group' ? (
						<FilterGroupComponent
							group={filter as FilterGroup}
							onUpdate={(updated) => updateFilter(index, updated)}
							onDelete={() => deleteFilter(index)}
						/>
					) : (
						<FilterComponent
							filter={filter}
							onUpdate={(updated) => updateFilter(index, updated)}
							onDelete={() => deleteFilter(index)}
							addFilter={() => insertFilter(index + 1)}
						/>
					)}
					{index < group.filters.length - 1 && <Operator or={group.operator === 'OR'} />}
				</div>
			))}

			<div className="addfilter flex gap-5">
				<button onClick={addFilter} type="button" className="flex items-center">
					<img className="mr-2.5" src={iconAdd} alt="" width={16} />
					Add Filter
				</button>
				{group.filters.length > 0 && (
					<button type="button" onClick={addNestedGroup} className="flex items-center">
						<img className="mr-2.5" src={iconAdd} alt="" width={16} />
						Add Nested Group
					</button>
				)}
			</div>
		</div>
	);
}
