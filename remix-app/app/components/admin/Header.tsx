import { Link, NavLink, useSubmit } from '@remix-run/react';
import { Menu } from '@headlessui/react';

import { useUser } from '~/utils/client-utils';

import logo from '~/images/Gun-Violence-Archive-Logo-Icon.svg';
import iconAdd from '~/images/Icon-Admin-Add.svg';
import iconArrowDown from '~/images/Icon-Arrow-Down.svg';
import iconArrowUp from '~/images/Icon-Arrow-Up.svg';

export const AdminHeader = () => {
	const user = useUser();
	const submit = useSubmit();

	const onSignOut = () => {
		submit({}, { action: '/user/logout', method: 'POST' });
	};

	return (
		<header className="sticky top-0 z-50">
			<div className="xl:flex xl:flex-row-reverse justify-between space-x-3 bg-gray-800 pb-3 xl:pb-0">
				<div className="flex justify-end items-center space-x-10">
					{(user.role == 'Admin' || user.role == 'Editor') && (
						<div className="text-sm text-white">
							<a href="/admin/incident/new">
								<div className="flex">
									<img className="mr-2.5" src={iconAdd} alt="" width={16} />
									Add Incident
								</div>
							</a>
						</div>
					)}
					<div className="h-full text-lg text-white">
						<Menu as="div" className="relative inline-block h-full text-left z-10">
							{({ open }) => (
								<>
									<Menu.Button className="inline-flex h-full min-w-40 xl:min-w-52 items-center bg-orange-500 px-5">
										{user.name}
										<img
											className="ml-2.5"
											src={open ? iconArrowUp : iconArrowDown}
											alt=""
											width={8}
										/>
									</Menu.Button>
									<Menu.Items className="absolute right-0 w-full divide-y divide-orange-500 bg-orange-800">
										<Menu.Item>
											<Link
												className="block h-[60px] w-full pl-5 leading-[60px]"
												to="/user/profile"
											>
												My Account
											</Link>
										</Menu.Item>
										<Menu.Item>
											<button
												type="button"
												className="block h-[60px] w-full pl-5 text-left"
												onClick={onSignOut}
											>
												Sign Out
											</button>
										</Menu.Item>
									</Menu.Items>
								</>
							)}
						</Menu>
					</div>
				</div>
				<div className="flex items-center space-x-3 xl:space-x-10">
					<div>
						<Link to={`/`}>
							<img src={logo} alt="Gun Violence Archive" width={60} />
						</Link>
					</div>
					{(user.role == 'Admin' || user.role == 'Editor') && (
						<ul className="flex flex-wrap space-x-3 xl:space-x-8 xl:text-xl font-bold text-white">
							<li>
								<NavLink
									to="/admin/workspace"
									className={({ isActive }) =>
										isActive
											? 'underline underline-offset-10 decoration-3 decoration-orange-500'
											: ''
									}
								>
									Workspace
								</NavLink>
							</li>
							<li>
								<NavLink
									to="/admin/incident"
									className={({ isActive }) =>
										isActive
											? 'underline underline-offset-10 decoration-3 decoration-orange-500'
											: ''
									}
								>
									Incidents
								</NavLink>
							</li>
							{user.role == 'Admin' && (
								<>
									<li>
										<NavLink
											to="/admin/dashboard"
											className={({ isActive }) =>
												isActive
													? 'underline underline-offset-10 decoration-3 decoration-orange-500'
													: ''
											}
										>
											Dashboards
										</NavLink>
									</li>
									<li>
										<NavLink
											to="/admin/user"
											reloadDocument
											className={({ isActive }) =>
												isActive
													? 'underline underline-offset-10 decoration-3 decoration-orange-500'
													: ''
											}
										>
											User Management
										</NavLink>
									</li>
									<li>
										<Menu as="div" className="relative inline-block h-full text-left">
											{({ open }) => (
												<>
													<Menu.Button className="inline-flex h-full items-center">
														Settings
														<img
															className="ml-2.5"
															src={open ? iconArrowUp : iconArrowDown}
															alt=""
															width={8}
														/>
													</Menu.Button>
													<Menu.Items className="absolute -left-5 top-10 min-w-80 divide-y divide-gray-500 bg-gray-800">
														<Menu.Item>
															<Link
																className="block h-[60px] w-full pl-5 leading-[60px]"
																to="/admin/toll"
															>
																Manage Toll
															</Link>
														</Menu.Item>
														<Menu.Item>
															<Link
																className="block h-[60px] w-full pl-5 leading-[60px]"
																to="/admin/column"
															>
																Manage Column Templates
															</Link>
														</Menu.Item>
														<Menu.Item>
															<Link
																className="block h-[60px] w-full pl-5 leading-[60px]"
																to="/admin/configuration"
															>
																Manage Configurations
															</Link>
														</Menu.Item>
														<Menu.Item>
															<Link
																className="block h-[60px] w-full pl-5 leading-[60px]"
																to="/admin/taxonomy"
															>
																Manage Taxonomy
															</Link>
														</Menu.Item>
														<Menu.Item>
															<Link
																className="block h-[60px] w-full pl-5 leading-[60px]"
																to="/admin/token"
															>
																Generate Token
															</Link>
														</Menu.Item>
													</Menu.Items>
												</>
											)}
										</Menu>
									</li>
									<li>
										<Menu as="div" className="relative inline-block h-full text-left">
											{({ open }) => (
												<>
													<Menu.Button className="inline-flex h-full items-center">
														Reports
														<img
															className="ml-2.5"
															src={open ? iconArrowUp : iconArrowDown}
															alt=""
															width={8}
														/>
													</Menu.Button>
													<Menu.Items className="absolute -left-5 top-10 min-w-80 divide-y divide-gray-500 bg-gray-800">
														<Menu.Item>
															<Link
																className="block h-[60px] w-full pl-5 leading-[60px]"
																to="/admin/reports/geocode"
															>
																Geocode Error Logs
															</Link>
														</Menu.Item>
													</Menu.Items>
												</>
											)}
										</Menu>
									</li>
									<li>
										<Menu as="div" className="relative inline-block h-full text-left">
											{({ open }) => (
												<>
													<Menu.Button className="inline-flex h-full items-center">
														System
														<img
															className="ml-2.5"
															src={open ? iconArrowUp : iconArrowDown}
															alt=""
															width={8}
														/>
													</Menu.Button>
													<Menu.Items className="absolute -left-5 top-10 min-w-80 divide-y divide-gray-500 bg-gray-800">
														<Menu.Item>
															<Link
																className="block h-[60px] w-full pl-5 leading-[60px]"
																to="/admin/system/health"
															>
																Site Health
															</Link>
														</Menu.Item>
														<Menu.Item>
															<Link
																className="block h-[60px] w-full pl-5 leading-[60px]"
																to="/admin/system/migrations"
															>
																Database Migrations
															</Link>
														</Menu.Item>
														<Menu.Item>
															<Link
																className="block h-[60px] w-full pl-5 leading-[60px]"
																to="/admin/system/errors"
															>
																Error Logs
															</Link>
														</Menu.Item>
													</Menu.Items>
												</>
											)}
										</Menu>
									</li>
								</>
							)}
						</ul>
					)}
				</div>
			</div>
		</header>
	);
};
