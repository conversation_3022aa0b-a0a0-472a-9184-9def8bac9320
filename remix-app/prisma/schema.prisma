generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["gva_data", "site"]
}

model users {
  id               Int      @id @default(autoincrement())
  name             String
  email            String
  org              String?
  password         String
  phone            String?
  activation_token String?
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt
  status           Boolean  @default(false)
  locked           Boolean  @default(false)

  users_roles                users_roles[]
  users_sessions             users_sessions[]
  users_login_history        users_login_history[]
  gva_entry_column_templates gva_entry_column_templates[]

  @@schema("site")
}

model roles {
  id         Int      @id @default(autoincrement())
  name       String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  status     Boolean  @default(true)
  weight     Int      @default(0)

  users_roles users_roles[]

  @@schema("site")
}

model users_roles {
  id  Int @id @default(autoincrement())
  uid Int
  rid Int

  role roles @relation(fields: [rid], references: [id])
  user users @relation(fields: [uid], references: [id])

  @@schema("site")
}

model users_sessions {
  id         Int      @id @default(autoincrement())
  uid        Int
  user_agent String
  expires_at DateTime
  token      String
  code       Int
  ip         String

  user users @relation(fields: [uid], references: [id])

  @@schema("site")
}

model users_login_history {
  id        Int      @id @default(autoincrement())
  uid       Int
  timestamp DateTime @default(now())
  ip        String

  user users @relation(fields: [uid], references: [id])

  @@schema("site")
}

model config {
  id     Int    @id @default(autoincrement())
  key    String @unique @db.VarChar(255)
  values String

  @@schema("site")
}

model dashboard {
  id           Int      @id @default(autoincrement())
  name         String
  path         String
  dashboard_id String
  is_active    Boolean  @default(false)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
  weight       Int      @default(0)
  on_main_menu Boolean  @default(false)

  @@unique([dashboard_id, path])
  @@schema("site")
}

model content {
  id         Int    @id @default(autoincrement())
  content    String
  created_by Int    @default(0)
  contentid  String @unique @default("")

  @@schema("site")
}

model migration_tracking {
  id             Int       @id @default(autoincrement())
  migration_name String    @unique
  status         String    // "pending", "completed", "failed", "skipped"
  applied_at     DateTime?
  error_message  String?
  created_at     DateTime  @default(now())
  updated_at     DateTime  @updatedAt

  @@schema("site")
}

model files {
  fid            Int               @id @default(autoincrement())
  uid            Int               @default(0)
  filename       String            @default("") @db.VarChar(255)
  uri            String            @default("") @db.VarChar(255)
  filemime       String            @default("") @db.VarChar(255)
  filesize       BigInt            @default(0)
  status         Boolean           @default(false)
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  incidentSource incident_sources?

  @@schema("site")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model airportCodes {
  st           String? @db.VarChar(128)
  locid        String? @db.Char(3)
  city         String? @db.VarChar(35)
  facilityname String? @db.VarChar(128)
  FAA_Region   String? @map("FAA Region") @db.Char(3)
  county       String? @db.VarChar(45)
  use          String? @db.Char(2)
  sitenumber   String? @db.VarChar(128)
  arplatitude  String? @db.VarChar(128)
  arplongitude String? @db.VarChar(128)

  @@ignore
  @@schema("gva_data")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model deleted_entities_log {
  entity_id     Int
  entity_bundle String? @db.VarChar
  deleted       Int?

  @@index([entity_bundle], map: "delete_log_entity_bundle_idx")
  @@index([entity_id], map: "delete_log_entity_id_idx")
  @@ignore
  @@schema("gva_data")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model districts {
  state String? @db.VarChar(255)
  name  String? @db.VarChar(255)

  @@ignore
  @@schema("gva_data")
}

model gva_entry_city_aliases {
  id              Int    @id(map: "gva_entry_city_aliases_pk")
  original_city   String @default("") @db.VarChar(35)
  original_alias  String @default("") @db.VarChar(35)
  original_county String @default("") @db.VarChar(45)
  original_state  String @default("") @db.VarChar(45)
  new_alias       String @default("") @db.VarChar(35)
  new_county      String @default("") @db.VarChar(45)
  override        Int    @default(0) @db.SmallInt
  action_on       String @default("") @db.VarChar(12)

  @@index([id])
  @@schema("gva_data")
}

model incident_guns {
  incident_id    Int?
  guns_entity_id Int     @id(map: "incident_guns_pk") @default(autoincrement())
  weight         Int?
  stolen_value   String? @db.VarChar(255)
  gun_type_tid   Int?
  changed_date   Int?

  incident incidents? @relation(fields: [incident_id], references: [incident_id])
  gun_type taxonomy?  @relation(fields: [gun_type_tid], references: [tid])

  @@schema("gva_data")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model incident_guns_pending {
  incident_id    Int?
  guns_entity_id Int
  weight         Int?
  stolen_value   String? @db.VarChar(255)
  gun_type_tid   Int?
  changed_date   Int?

  @@ignore
  @@schema("gva_data")
}

model incident_guns_temp {
  incident_id    Int?
  changed_date   Int?
  guns_entity_id Int     @id(map: "incident_guns_temp_pk")
  weight         Int?
  stolen_value   String?
  gun_type_tid   Int?

  @@schema("gva_data")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model incident_participants {
  participant_id                  Int     @id(map: "incident_participants_pk") @default(autoincrement())
  incident_id                     Int?
  weight                          Int?
  name                            String? @db.VarChar(255)
  name_alias                      String? @db.VarChar(255)
  participants_characteristic_tid Int?
  participant_status_tid          String?
  participant_type                String? @db.VarChar(255)
  relationship_tid                Int?
  gender                          String? @db.VarChar(255)
  age                             Int?
  age_group_tid                   Int?
  changed_date                    Int?

  incident  incidents? @relation(fields: [incident_id], references: [incident_id])
  age_group taxonomy?  @relation(fields: [age_group_tid], references: [tid])

  @@index([incident_id], map: "b_incident_participants_incident_id_idx")
  @@index([participant_id, participant_status_tid, participant_type, incident_id], map: "b_incident_participants_incident_id_participant_status_tid_part")
  @@index([participant_id], map: "b_incident_participants_participant_id_idx")
  @@schema("gva_data")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model incident_participants_pending {
  participant_id                  Int
  incident_id                     Int?
  weight                          Int?
  name                            String? @db.VarChar(255)
  participants_characteristic_tid Int?
  participant_status_tid          String?
  participant_type                String? @db.VarChar(255)
  relationship_tid                Int?
  gender                          String? @db.VarChar(255)
  age                             Int?
  age_group_tid                   Int?
  changed_date                    Int?

  @@ignore
  @@schema("gva_data")
}

model incident_participants_temp {
  participant_id                  Int     @id(map: "incident_participants_temp_pk")
  incident_id                     Int?
  changed_date                    Int?
  weight                          Int?
  name                            String?
  participants_characteristic_tid Int?
  participant_status_tid          String?
  participant_type                String?
  relationship_tid                Int?
  gender                          String?
  age                             Int?
  age_group_tid                   Int?

  @@schema("gva_data")
}

model incident_sources {
  incident_id      Int?
  source_entity_id Int     @id(map: "incident_sources_pk") @default(autoincrement())
  weight           Int?
  source_url       String? @db.VarChar(255)
  image_fid        Int?    @unique
  image_uri        String? @db.VarChar(255)
  image_title      String? @db.VarChar(1024)
  image_width      Int?
  image_height     Int?
  image_alt        String? @db.VarChar(512)
  changed_date     Int?
  source_name      String?

  incident  incidents? @relation(fields: [incident_id], references: [incident_id])
  imageFile files?     @relation(fields: [image_fid], references: [fid])

  @@index([source_entity_id])
  @@schema("gva_data")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model incident_sources_pending {
  incident_id      Int?
  source_entity_id Int
  weight           Int?
  source_url       String? @db.VarChar(255)
  image_fid        Int?
  image_uri        String? @db.VarChar(255)
  image_title      String? @db.VarChar(1024)
  image_width      Int?
  image_height     Int?
  image_alt        String? @db.VarChar(512)
  changed_date     Int?
  source_name      String?

  @@ignore
  @@schema("gva_data")
}

model incident_sources_temp {
  incident_id      Int?
  changed_date     Int?
  source_entity_id Int     @id(map: "incident_sources_temp_pk")
  weight           Int?
  source_name      String?
  source_url       String?
  image_fid        Int?
  image_uri        String?
  image_title      String?
  image_width      Int?
  image_height     Int?
  image_alt        String?

  @@schema("gva_data")
}

model incident_types {
  incident_id  Int
  type_tid     Int
  weight       Int?
  changed_date Int?

  incident incidents? @relation(fields: [incident_id], references: [incident_id])
  type     taxonomy?  @relation(fields: [type_tid], references: [tid])

  @@id([incident_id, type_tid], map: "incident_types_pk")
  @@index([incident_id, type_tid], map: "b_incident_types_incident_id_idx")
  @@index([type_tid])
  @@schema("gva_data")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model incident_types_pending {
  incident_id  Int
  type_tid     Int
  weight       Int?
  changed_date Int?

  @@ignore
  @@schema("gva_data")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model incident_types_prod {
  incident_id  Int?
  changed_date Int?
  type_tid     Int?
  weight       Int?
  count        Int?

  @@index([incident_id, type_tid], map: "incident_types_prod_incident_id_idx")
  @@ignore
  @@schema("gva_data")
}

model incident_types_temp {
  incident_id  Int
  changed_date Int?
  type_tid     Int
  weight       Int?

  @@id([incident_id, type_tid], map: "incident_types_temp_pk")
  @@index([incident_id])
  @@schema("gva_data")
}

model incident_validators {
  incident_id   Int
  validator_tid Int
  weight        Int?
  changed_date  Int?

  incident  incidents? @relation(fields: [incident_id], references: [incident_id])
  validator taxonomy?  @relation(fields: [validator_tid], references: [tid])

  @@id([incident_id, validator_tid], map: "incident_validators_pk")
  @@index([incident_id, validator_tid], map: "b_incident_validators_incident_id_idx")
  @@schema("gva_data")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model incident_validators_pending {
  incident_id   Int
  validator_tid Int
  weight        Int?
  changed_date  Int?

  @@ignore
  @@schema("gva_data")
}

model incident_validators_temp {
  incident_id   Int
  changed_date  Int?
  validator_tid Int
  weight        Int?

  @@id([incident_id, validator_tid], map: "incident_validators_temp_pk")
  @@schema("gva_data")
}

model incidents {
  incident_id              Int     @id(map: "incidents_pk") @default(autoincrement())
  uuid                     String  @unique @db.Char(36)
  author_uid               Int?
  last_changed_by_uid      Int?
  created_date             Int?
  changed_date             Int?
  incident_date            Int?
  incident_time            Int?
  state_taxonomy_id        Int?
  city_county              String? @db.VarChar(255)
  address                  String? @db.VarChar(255)
  notes                    String?
  pending                  Int?
  twitter_source           Int?
  congressional_district   String? @db.VarChar(50)
  state_senate_district    String? @db.VarChar(50)
  state_house_district     String? @db.VarChar(50)
  latitude                 Float?  @db.Real
  longitude                Float?  @db.Real
  county                   String? @db.VarChar(255)
  business                 String? @db.VarChar(255)
  guns_involved_counter    Int?
  gelocation_override      Int?
  districts_override       Int?
  drop_off                 Int?
  approximate_time         Int?
  geocode_notes            String?
  related                  String? @db.VarChar(255)
  status                   Int     @default(1) @db.SmallInt
  private_notes            String?
  neighborhood             String? @db.VarChar(255)
  matched_city             String? @db.VarChar(255)
  google_auto_complete     Boolean @default(false)
  zipcode                  String? @db.VarChar(10)
  incident_alias           String? @db.VarChar(255)
  incident_timezone_offset String?

  incident_guns                incident_guns[]
  incident_participants        incident_participants[]
  incident_sources             incident_sources[]
  incident_types               incident_types[]
  incident_validators          incident_validators[]
  incident_location_categories incident_location_categories[]
  state                        taxonomy?                      @relation(fields: [state_taxonomy_id], references: [tid])
  gva_general_fetch_queue      gva_general_fetch_queue[]
  incident_locations           incident_locations?            @relation("IncidentLocation")

  @@index([incident_id], map: "b_incidents_incident_id_idx")
  @@schema("gva_data")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model incidents_pending {
  incident_id            Int
  uuid                   String? @db.Char(36)
  author_uid             Int?
  last_changed_by_uid    Int?
  created_date           Int?
  changed_date           Int?
  incident_date          Int?
  incident_time          Int?
  state_taxonomy_id      Int?
  city_county            String? @db.VarChar(255)
  address                String? @db.VarChar(255)
  notes                  String?
  pending                Int?
  twitter_source         Int?
  congressional_district String? @db.VarChar(50)
  state_senate_district  String? @db.VarChar(50)
  state_house_district   String? @db.VarChar(50)
  latitude               Float?  @db.Real
  longitude              Float?  @db.Real
  county                 String? @db.VarChar(255)
  business               String? @db.VarChar(255)
  guns_involved_counter  Int?
  gelocation_override    Int?
  districts_override     Int?
  drop_off               Int?
  approximate_time       Int?
  geocode_notes          String?
  related                String? @db.VarChar(255)

  @@ignore
  @@schema("gva_data")
}

model incidents_temp {
  incident_id            Int     @id(map: "incidents_temp_pk")
  uuid                   String?
  author_uid             Int?
  last_changed_by_uid    Int?
  created_date           Int?
  changed_date           Int?
  incident_date          Int?
  incident_time          Int?
  state_taxonomy_id      Int?
  city_county            String?
  address                String?
  notes                  String?
  pending                Int?
  twitter_source         Int?
  congressional_district String?
  state_senate_district  String?
  state_house_district   String?
  latitude               Float?
  longitude              Float?
  county                 String?
  business               String?
  guns_involved_counter  Int?
  gelocation_override    Int?
  districts_override     Int?
  drop_off               Int?
  approximate_time       Int?
  geocode_notes          String?
  related                String?

  @@schema("gva_data")
}

model incident_location_categories {
  incident_id  Int
  category_tid Int
  weight       Int?
  changed_date Int?

  incident incidents? @relation(fields: [incident_id], references: [incident_id])
  category taxonomy?  @relation(fields: [category_tid], references: [tid])

  @@id([incident_id, category_tid], map: "incident_location_categories_pk")
  @@schema("gva_data")
}

model incident_locations {
  incident_id                       Int     @id
  metro_micro_statistical_area      String? @db.VarChar(255)
  metro_micro_statistical_area_type String? @db.VarChar(255)
  combined_statistical_area         String? @db.VarChar(255)
  metropolitan_division             String?
  county_fips                       String? @db.VarChar(10)
  state_fips                        String? @db.VarChar(10)
  tract_code                        String? @db.VarChar(10)
  block_code                        String? @db.VarChar(10)
  block_group                       String? @db.VarChar(10)
  full_fips                         String?
  county_subdivision_name           String?
  county_subdivision_fips           String?
  county_subdivision_class_code     String?
  place_fips                        String?
  school_districts                  String?
  changed_date                      Int?

  incident incidents? @relation("IncidentLocation", fields: [incident_id], references: [incident_id])

  @@schema("gva_data")
}

model taxonomy {
  tid                   Int     @id @default(autoincrement())
  vid                   Int?
  taxonomy_name         String? @db.VarChar(255)
  taxonomy_machine_name String  @db.VarChar(255)
  value                 String  @db.VarChar(255)
  weight                Int?    @default(0)
  parent                Int?    @default(0)
  description           String?
  header                String? @db.VarChar(255)

  incidents                    incidents[]
  incident_guns                incident_guns[]
  incident_validators          incident_validators[]
  incident_participants        incident_participants[]
  incident_types               incident_types[]
  incident_location_categories incident_location_categories[]

  @@index([value])
  @@schema("gva_data")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model zipcodes {
  zipcode                   String   @db.Char(5)
  primaryrecord             String?  @db.Char(1)
  population                Int?
  householdsperzipcode      Int?
  whitepopulation           Int?
  blackpopulation           Int?
  hispanicpopulation        Int?
  asianpopulation           Int?
  hawaiianpopulation        Int?
  indianpopulation          Int?
  otherpopulation           Int?
  malepopulation            Int?
  femalepopulation          Int?
  personsperhousehold       Decimal? @db.Decimal(4, 2)
  averagehousevalue         Int?
  incomeperhousehold        Int?
  latitude                  Decimal? @db.Decimal(12, 6)
  longitude                 Decimal? @db.Decimal(12, 6)
  elevation                 Int?
  state                     String?  @db.Char(2)
  statefullname             String?  @db.VarChar(35)
  citytype                  String?  @db.Char(1)
  cityaliasabbreviation     String?  @db.VarChar(13)
  areacode                  String?  @db.VarChar(55)
  city                      String?  @db.VarChar(35)
  cityaliasname             String?  @db.VarChar(35)
  county                    String?  @db.VarChar(45)
  countyfips                String?  @db.Char(5)
  statefips                 String?  @db.Char(2)
  timezone                  String?  @db.Char(2)
  daylightsaving            String?  @db.Char(1)
  msa                       String?  @db.VarChar(35)
  pmsa                      String?  @db.Char(4)
  csa                       String?  @db.Char(3)
  cbsa                      String?  @db.Char(5)
  cbsa_div                  String?  @db.Char(5)
  cbsa_type                 String?  @db.Char(5)
  cbsa_name                 String?  @db.VarChar(150)
  msa_name                  String?  @db.VarChar(150)
  pmsa_name                 String?  @db.VarChar(150)
  region                    String?  @db.VarChar(10)
  division                  String?  @db.VarChar(20)
  mailingname               String?  @db.Char(1)
  preferredlastlinekey      String?  @db.VarChar(10)
  classificationcode        String?  @db.Char(1)
  multicounty               String?  @db.Char(1)
  csaname                   String?  @db.VarChar(150)
  cbsa_div_name             String?  @db.VarChar(150)
  citystatekey              String?  @db.Char(6)
  cityaliascode             String?  @db.VarChar(5)
  citymixedcase             String?  @db.VarChar(35)
  cityaliasmixedcase        String?  @db.VarChar(35)
  stateansi                 String?  @db.VarChar(2)
  countyansi                String?  @db.VarChar(3)
  facilitycode              String?  @db.Char(1)
  citydeliveryindicator     String?  @db.Char(1)
  carrierrouteratesortation String?  @db.Char(1)
  financenumber             String?  @db.VarChar(10)
  uniquezipname             String?  @db.Char(1)
  countymixedcase           String?  @db.VarChar(45)

  @@index([areacode], map: "index_zipcodes_areacode")
  @@index([city], map: "index_zipcodes_city")
  @@index([cityaliasname], map: "index_zipcodes_cityaliasname")
  @@index([citystatekey], map: "index_zipcodes_citystatekey")
  @@index([county], map: "index_zipcodes_county")
  @@index([latitude], map: "index_zipcodes_latitude")
  @@index([longitude], map: "index_zipcodes_longitude")
  @@index([primaryrecord], map: "index_zipcodes_primaryrecord")
  @@index([state], map: "index_zipcodes_state")
  @@index([zipcode], map: "index_zipcodes_zipcode")
  @@ignore
  @@schema("gva_data")
}

model toll_source_modifications {
  id           Int      @id @default(autoincrement())
  title        String   @db.VarChar(255)
  year         Int
  type         String   @default("National") @db.VarChar(50)
  state        String?  @db.VarChar(50)
  city         String?  @db.VarChar(50)
  modification String?
  notes        String?
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  @@schema("gva_data")
}

model gva_entry_column_templates {
  ctuuid                 String  @id @db.VarChar(36)
  name                   String  @unique @db.VarChar(255)
  type                   String  @db.VarChar(32)
  overridden             Int     @default(0) @db.SmallInt
  source                 String? @db.VarChar(32)
  supported_export_types String?
  columns                String?
  uid                    Int?
  created                Int?
  changed                Int?

  author users? @relation(fields: [uid], references: [id])

  @@schema("gva_data")
}

model gva_general_fetch_queue {
  entity_id     Int     @id
  status        Int     @default(1) @db.SmallInt
  added         Int
  comment       String?
  json_response String?
  error_type    String? @db.VarChar(255)

  incident incidents? @relation(fields: [entity_id], references: [incident_id])

  @@schema("gva_data")
}

model gva_general_sources_queue {
  entity_id Int @id
  timestamp Int

  @@schema("gva_data")
}

model SavedSearch {
  id                String   @id @default(uuid())
  name              String
  filters           Json     // Stores the filter array
  columns           String[] // Stores column keys
  grouping          String   // 'AND' or 'OR'
  userId            String
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  visualization     String?  // Stores the visualization key
  visualizationConfig Json?   // Stores the visualization configuration

  @@map("saved_searches") // Maps to the actual table name in the database
  @@schema("site")
}

// SearchHistory model has been removed
