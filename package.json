{"name": "gva.web", "version": "0.1.0", "bin": {"gva.web": "bin/gva.web.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.4", "@types/node": "20.5.9", "jest": "^29.6.4", "ts-jest": "^29.1.1", "aws-cdk": "2.178.2", "ts-node": "^10.9.1", "typescript": "~5.2.2"}, "dependencies": {"aws-cdk-lib": "2.95.1", "constructs": "^10.0.0", "source-map-support": "^0.5.21"}}